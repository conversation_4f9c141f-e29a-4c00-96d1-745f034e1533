{"name": "pg-cloudflare", "version": "1.1.1", "description": "A socket implementation that can run on Cloudflare Workers using native TCP connections.", "main": "dist/empty.js", "types": "dist/index.d.ts", "license": "MIT", "devDependencies": {"ts-node": "^8.5.4", "typescript": "^4.0.3"}, "exports": {"workerd": "./dist/index.js", "default": "./dist/empty.js"}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "prepublish": "yarn build", "test": "echo e2e test in pg package"}, "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg-cloudflare"}, "files": ["/dist/*{js,ts,map}", "/src"], "gitHead": "eaafac36dc8f4a13f1fecc9e3420d35559fd8e2b"}