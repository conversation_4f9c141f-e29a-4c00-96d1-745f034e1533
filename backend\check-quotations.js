const pool = require('./database');

async function checkQuotations() {
  try {
    console.log('Checking quotations database...');
    
    // Get recent quotations
    const result = await pool.query('SELECT id, reference_number, created_at FROM quotations ORDER BY created_at DESC LIMIT 10');
    console.log('Recent quotations:');
    console.log(result.rows);
    
    // Check if the specific ID exists
    const specificResult = await pool.query('SELECT * FROM quotations WHERE id = $1', ['1094896457040658400']);
    console.log('Specific ID check for 1094896457040658400:');
    console.log(specificResult.rows);
    
    // Check the sequence current value
    const sequenceResult = await pool.query("SELECT last_value FROM quotations_id_seq");
    console.log('Current sequence value:', sequenceResult.rows);
    
    await pool.end();
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkQuotations();
