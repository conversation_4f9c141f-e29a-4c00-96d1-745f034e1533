// prettier-ignore
/* eslint-disable */
// @ts-nocheck
/* auto-generated by NAPI-RS */

const { readFileSync } = require('fs')
let nativeBinding = null
const loadErrors = []

const isMusl = () => {
  let musl = false
  if (process.platform === 'linux') {
    musl = isMuslFromFilesystem()
    if (musl === null) {
      musl = isMuslFromReport()
    }
    if (musl === null) {
      musl = isMuslFromChildProcess()
    }
  }
  return musl
}

const isFileMusl = (f) => f.includes('libc.musl-') || f.includes('ld-musl-')

const isMuslFromFilesystem = () => {
  try {
    return readFileSync('/usr/bin/ldd', 'utf-8').includes('musl')
  } catch {
    return null
  }
}

const isMuslFromReport = () => {
  const report = typeof process.report.getReport === 'function' ? process.report.getReport() : null
  if (!report) {
    return null
  }
  if (report.header && report.header.glibcVersionRuntime) {
    return false
  }
  if (Array.isArray(report.sharedObjects)) {
    if (report.sharedObjects.some(isFileMusl)) {
      return true
    }
  }
  return false
}

const isMuslFromChildProcess = () => {
  try {
    return require('child_process').execSync('ldd --version', { encoding: 'utf8' }).includes('musl')
  } catch (e) {
    // If we reach this case, we don't know if the system is musl or not, so is better to just fallback to false
    return false
  }
}

function requireNative() {
  if (process.env.NAPI_RS_NATIVE_LIBRARY_PATH) {
    try {
      nativeBinding = require(process.env.NAPI_RS_NATIVE_LIBRARY_PATH)
    } catch (err) {
      loadErrors.push(err)
    }
  } else if (process.platform === 'android') {
    if (process.arch === 'arm64') {
      try {
        return require('./skia.android-arm64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-android-arm64')
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'arm') {
      try {
        return require('./skia.android-arm-eabi.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-android-arm-eabi')
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on Android ${process.arch}`))
    }
  } else if (process.platform === 'win32') {
    if (process.arch === 'x64') {
      try {
        return require('./skia.win32-x64-msvc.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-win32-x64-msvc')
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'ia32') {
      try {
        return require('./skia.win32-ia32-msvc.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-win32-ia32-msvc')
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'arm64') {
      try {
        return require('./skia.win32-arm64-msvc.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-win32-arm64-msvc')
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on Windows: ${process.arch}`))
    }
  } else if (process.platform === 'darwin') {
    try {
      return require('./skia.darwin-universal.node')
    } catch (e) {
      loadErrors.push(e)
    }
    try {
      return require('@napi-rs/canvas-darwin-universal')
    } catch (e) {
      loadErrors.push(e)
    }

    if (process.arch === 'x64') {
      try {
        return require('./skia.darwin-x64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-darwin-x64')
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'arm64') {
      try {
        return require('./skia.darwin-arm64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-darwin-arm64')
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on macOS: ${process.arch}`))
    }
  } else if (process.platform === 'freebsd') {
    if (process.arch === 'x64') {
      try {
        return require('./skia.freebsd-x64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-freebsd-x64')
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'arm64') {
      try {
        return require('./skia.freebsd-arm64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-freebsd-arm64')
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on FreeBSD: ${process.arch}`))
    }
  } else if (process.platform === 'linux') {
    if (process.arch === 'x64') {
      if (isMusl()) {
        try {
          return require('./skia.linux-x64-musl.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          return require('@napi-rs/canvas-linux-x64-musl')
        } catch (e) {
          loadErrors.push(e)
        }
      } else {
        try {
          return require('./skia.linux-x64-gnu.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          return require('@napi-rs/canvas-linux-x64-gnu')
        } catch (e) {
          loadErrors.push(e)
        }
      }
    } else if (process.arch === 'arm64') {
      if (isMusl()) {
        try {
          return require('./skia.linux-arm64-musl.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          return require('@napi-rs/canvas-linux-arm64-musl')
        } catch (e) {
          loadErrors.push(e)
        }
      } else {
        try {
          return require('./skia.linux-arm64-gnu.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          return require('@napi-rs/canvas-linux-arm64-gnu')
        } catch (e) {
          loadErrors.push(e)
        }
      }
    } else if (process.arch === 'arm') {
      if (isMusl()) {
        try {
          return require('./skia.linux-arm-musleabihf.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          return require('@napi-rs/canvas-linux-arm-musleabihf')
        } catch (e) {
          loadErrors.push(e)
        }
      } else {
        try {
          return require('./skia.linux-arm-gnueabihf.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          return require('@napi-rs/canvas-linux-arm-gnueabihf')
        } catch (e) {
          loadErrors.push(e)
        }
      }
    } else if (process.arch === 'riscv64') {
      if (isMusl()) {
        try {
          return require('./skia.linux-riscv64-musl.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          return require('@napi-rs/canvas-linux-riscv64-musl')
        } catch (e) {
          loadErrors.push(e)
        }
      } else {
        try {
          return require('./skia.linux-riscv64-gnu.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          return require('@napi-rs/canvas-linux-riscv64-gnu')
        } catch (e) {
          loadErrors.push(e)
        }
      }
    } else if (process.arch === 'ppc64') {
      try {
        return require('./skia.linux-ppc64-gnu.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-linux-ppc64-gnu')
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 's390x') {
      try {
        return require('./skia.linux-s390x-gnu.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        return require('@napi-rs/canvas-linux-s390x-gnu')
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on Linux: ${process.arch}`))
    }
  } else {
    loadErrors.push(new Error(`Unsupported OS: ${process.platform}, architecture: ${process.arch}`))
  }
}

nativeBinding = requireNative()

if (!nativeBinding || process.env.NAPI_RS_FORCE_WASI) {
  try {
    nativeBinding = require('./skia.wasi.cjs')
  } catch (err) {
    if (process.env.NAPI_RS_FORCE_WASI) {
      loadErrors.push(err)
    }
  }
  if (!nativeBinding) {
    try {
      nativeBinding = require('@napi-rs/canvas-wasm32-wasi')
    } catch (err) {
      if (process.env.NAPI_RS_FORCE_WASI) {
        loadErrors.push(err)
      }
    }
  }
}

if (!nativeBinding) {
  if (loadErrors.length > 0) {
    // TODO Link to documentation with potential fixes
    //  - The package owner could build/publish bindings for this arch
    //  - The user may need to bundle the correct files
    //  - The user may need to re-install node_modules to get new packages
    throw new Error('Failed to load native binding', { cause: loadErrors })
  }
  throw new Error(`Failed to load native binding`)
}

module.exports.GlobalFonts = nativeBinding.GlobalFonts
module.exports.CanvasElement = nativeBinding.CanvasElement
module.exports.CanvasGradient = nativeBinding.CanvasGradient
module.exports.CanvasPattern = nativeBinding.CanvasPattern
module.exports.CanvasRenderingContext2D = nativeBinding.CanvasRenderingContext2D
module.exports.FontKey = nativeBinding.FontKey
module.exports.Image = nativeBinding.Image
module.exports.ImageData = nativeBinding.ImageData
module.exports.Path = nativeBinding.Path
module.exports.SVGCanvas = nativeBinding.SVGCanvas
module.exports.ChromaSubsampling = nativeBinding.ChromaSubsampling
module.exports.clearAllCache = nativeBinding.clearAllCache
module.exports.convertSVGTextToPath = nativeBinding.convertSVGTextToPath
module.exports.FillType = nativeBinding.FillType
module.exports.PathOp = nativeBinding.PathOp
module.exports.StrokeCap = nativeBinding.StrokeCap
module.exports.StrokeJoin = nativeBinding.StrokeJoin
module.exports.SvgExportFlag = nativeBinding.SvgExportFlag
