const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testQuotationsAPI() {
  try {
    console.log('Testing quotations API...');
    
    const response = await fetch('http://localhost:8000/api/quotations');
    const quotations = await response.json();
    
    console.log('API Response:');
    quotations.forEach(q => {
      console.log(`ID: ${q.quotation_id} (type: ${typeof q.quotation_id}), Reference: ${q.reference_number}`);
    });
    
    // Test if we can get the specific quotation
    if (quotations.length > 0) {
      const firstQuotation = quotations[0];
      console.log(`\nTesting get by ID for: ${firstQuotation.quotation_id}`);
      
      const singleResponse = await fetch(`http://localhost:8000/api/quotations/${firstQuotation.quotation_id}`);
      if (singleResponse.ok) {
        const singleQuotation = await singleResponse.json();
        console.log('Single quotation retrieved successfully:', singleQuotation.quotation_id);
      } else {
        console.log('Failed to retrieve single quotation:', singleResponse.status);
      }
    }
    
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testQuotationsAPI();
