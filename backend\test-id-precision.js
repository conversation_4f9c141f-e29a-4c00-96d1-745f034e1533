// Test JavaScript number precision with large IDs

const actualId = '1094896457040658433';
const wrongId = '1094896457040658400';

console.log('Actual ID from database:', actualId);
console.log('Wrong ID from error:', wrongId);
console.log('Difference:', parseInt(actualId) - parseInt(wrongId));

// Test JavaScript number precision
const largeNumber = 1094896457040658433;
console.log('Large number as integer:', largeNumber);
console.log('Large number as string:', largeNumber.toString());
console.log('Is it safe integer?', Number.isSafeInteger(largeNumber));
console.log('Max safe integer:', Number.MAX_SAFE_INTEGER);

// Test what happens when we parse the string
const parsed = parseInt(actualId);
console.log('Parsed from string:', parsed);
console.log('Parsed equals original?', parsed.toString() === actualId);

// Test JSON parsing
const jsonTest = JSON.stringify({ id: actualId });
console.log('JSON stringified:', jsonTest);
const jsonParsed = JSON.parse(jsonTest);
console.log('JSON parsed back:', jsonParsed.id);
